# Database Function Changes Analysis - Declarative Schema Migration

## Executive Summary

**Total Functions Analyzed**: 38 functions in the declarative schema migration
**Functions with Changes**: 15 functions have significant changes
**New Functions**: 3 functions appear to be new
**Functions with Potential Issues**: 7 functions require immediate review

## Critical Issues Identified

### 🚨 HIGH PRIORITY - Functions with Lost Security Settings

#### 1. `calculate_unit_item_cost` - SECURITY DEFINER REMOVED
**Previous Version** (20250310143526):
```sql
CREATE OR REPLACE FUNCTION public.calculate_unit_item_cost (
    p_material_rate NUMERIC,
    p_labor_rate NUMERIC,
    p_productivity_per_hour NUMERIC
) RETURNS NUMERIC
SET search_path = '' AS $$
-- No SECURITY DEFINER
```

**New Version** (Declarative):
```sql
CREATE OR REPLACE FUNCTION public.calculate_unit_item_cost (
    p_material_rate numeric,
    p_labor_rate numeric,
    p_productivity_per_hour numeric
) RETURNS numeric LANGUAGE plpgsql
SET search_path TO '' AS $function$
-- Still no SECURITY DEFINER - this is correct
```
**Status**: ✅ CORRECT - This function doesn't need SECURITY DEFINER

#### 2. `get_organization_by_name` - SECURITY DEFINER REMOVED
**Previous Version** (20250510000001):
```sql
CREATE OR REPLACE FUNCTION public.get_organization_by_name (org_name_param TEXT) 
RETURNS TABLE (...) LANGUAGE plpgsql
SET search_path = '' AS $$
-- No SECURITY DEFINER in original
```

**New Version** (Declarative):
```sql
CREATE OR REPLACE FUNCTION public.get_organization_by_name (org_name_param text) 
RETURNS TABLE (...) LANGUAGE plpgsql SECURITY DEFINER
SET search_path TO '' AS $function$
```
**Status**: ✅ IMPROVED - Added SECURITY DEFINER correctly

### 🚨 HIGH PRIORITY - Functions with Changed Return Types

#### 3. `create_organization` - RETURN TYPE CHANGED
**Previous Version** (20250305094332):
```sql
CREATE OR REPLACE FUNCTION public.create_organization (
    name text,
    description text default null,
    logo_url text default null
) RETURNS json LANGUAGE plpgsql SECURITY DEFINER
```

**New Version** (Declarative):
```sql
CREATE OR REPLACE FUNCTION public.create_organization (
    name text,
    description text,
    logo_url text
) RETURNS uuid LANGUAGE plpgsql SECURITY DEFINER
```
**Status**: ⚠️ BREAKING CHANGE - Return type changed from `json` to `uuid`
**Impact**: This will break existing application code that expects JSON response

**🔧 IMPLEMENTATION STEPS:**

1. **Find all application dependencies:**
   ```bash
   # Search for all calls to create_organization
   grep -r "create_organization" src/
   grep -r "rpc.*create_organization" src/
   ```

2. **Check the original function implementation:**
   ```bash
   # View the complete original function
   cat supabase/migrations/20250305094332_add_organizations.sql | grep -A 30 "create_organization"
   ```

3. **Determine fix approach:**
   - **Option A**: Restore JSON return type (recommended if app expects JSON)
   - **Option B**: Update application code to handle UUID return

4. **If choosing Option A (restore JSON return):**
   ```sql
   CREATE OR REPLACE FUNCTION public.create_organization (
       name text,
       description text DEFAULT NULL,
       logo_url text DEFAULT NULL
   ) RETURNS json LANGUAGE plpgsql SECURITY DEFINER
   SET search_path TO '' AS $function$
   DECLARE
       new_org public.organization;
       user_id uuid := auth.uid();
   BEGIN
       -- Ensure user is authenticated
       IF user_id IS NULL THEN
           RAISE EXCEPTION 'Not authenticated';
       END IF;

       -- Create organization
       INSERT INTO public.organization(name, description, logo_url, created_by_user_id)
       VALUES (name, description, logo_url, user_id)
       RETURNING * INTO new_org;

       -- Return JSON object
       RETURN json_build_object(
           'org_id', new_org.org_id,
           'name', new_org.name,
           'description', new_org.description,
           'logo_url', new_org.logo_url
       );
   END;
   $function$;
   ```

5. **If choosing Option B (update app code):**
   - Update all calling code to handle UUID instead of JSON
   - Example: Change `data.org_id` to just `data` in application code

#### 4. `complete_project_stage` - RETURN TYPE CHANGED
**Previous Version** (20250310143526):
```sql
CREATE OR REPLACE FUNCTION public.complete_project_stage (
    p_project_stage_id UUID,
    p_completion_notes TEXT DEFAULT NULL
) RETURNS UUID
```

**New Version** (Declarative):
```sql
CREATE OR REPLACE FUNCTION public.complete_project_stage (
    p_project_stage_id uuid,
    p_completion_notes text
) RETURNS boolean LANGUAGE plpgsql SECURITY DEFINER
```
**Status**: ⚠️ BREAKING CHANGE - Return type changed from `UUID` to `boolean`
**Impact**: Application code expecting snapshot ID will break

**🔧 IMPLEMENTATION STEPS:**

1. **Find all application dependencies:**
   ```bash
   # Search for calls to complete_project_stage
   grep -r "complete_project_stage" src/
   grep -r "rpc.*complete_project_stage" src/
   ```

2. **Get the original function implementation:**
   ```bash
   # View the complete original function
   cat supabase/migrations/20250310143526_add_budget_management_functions.sql | grep -A 20 "complete_project_stage"
   ```

3. **Restore the original UUID return type:**
   ```sql
   CREATE OR REPLACE FUNCTION public.complete_project_stage (
       p_project_stage_id UUID,
       p_completion_notes TEXT DEFAULT NULL
   ) RETURNS UUID LANGUAGE plpgsql SECURITY DEFINER
   SET search_path TO '' AS $function$
   DECLARE
       v_project_id UUID;
       v_snapshot_id UUID;
       v_is_ready BOOLEAN;
   BEGIN
       -- Get project_id for permission check
       SELECT project_id INTO v_project_id
       FROM public.project_stage
       WHERE project_stage_id = p_project_stage_id;

       IF v_project_id IS NULL THEN
           RAISE EXCEPTION 'Project stage not found';
       END IF;

       -- Check if all checklist items are completed
       SELECT public.is_stage_ready_for_completion(p_project_stage_id) INTO v_is_ready;

       IF NOT v_is_ready THEN
           RAISE EXCEPTION 'Cannot complete stage: not all checklist items are completed';
       END IF;

       -- Create a budget snapshot
       SELECT public.create_budget_snapshot(p_project_stage_id, p_completion_notes) INTO v_snapshot_id;

       -- Update stage status to completed
       UPDATE public.project_stage
       SET status = 'completed',
           completed_at = NOW(),
           completion_notes = p_completion_notes
       WHERE project_stage_id = p_project_stage_id;

       RETURN v_snapshot_id;
   END;
   $function$;
   ```

4. **Verify the fix:**
   - Test that the function returns a valid snapshot UUID
   - Ensure application code can access the returned snapshot ID

### 🚨 HIGH PRIORITY - Functions with Logic Changes

#### 5. `import_budget_data` - SIMPLIFIED LOGIC
**Previous Version** (20250115000000): Complex implementation with detailed error handling, WBS creation, and comprehensive return object
**New Version** (Declarative): Simplified implementation with basic error array
**Status**: ⚠️ FUNCTIONALITY REDUCED - Lost detailed error handling and WBS creation logic

**📋 INVESTIGATION STEPS:**

1. **Get the complete original implementation:**
   ```bash
   # View the full original function (it's quite long)
   cat supabase/migrations/20250115000000_add_budget_import_function.sql
   ```

2. **Compare with current implementation:**
   ```bash
   # Extract current function from declarative schema
   grep -A 100 "import_budget_data" supabase/migrations/20250708095026_declarative_schemas.sql
   ```

3. **Find application usage:**
   ```bash
   # Search for import calls in the application
   grep -r "import_budget_data" src/
   find src/ -name "*.ts" -o -name "*.js" -o -name "*.svelte" | xargs grep -l "import.*budget"
   ```

4. **Check if WBS creation is still needed:**
   ```bash
   # Look for WBS-related import functionality in the app
   grep -r "wbs.*import\|import.*wbs" src/
   grep -r "wbs_library_item.*import" src/
   ```

5. **Determine what functionality to restore:**
   - **WBS Creation Logic**: Check if the app still needs automatic WBS item creation during import
   - **Error Handling**: Verify if detailed error reporting is used by the UI
   - **Return Object**: Check if the app expects the detailed return object with counts and timing

6. **Questions for stakeholders:**
   - Is automatic WBS library item creation during budget import still required?
   - Does the UI need detailed import statistics (timing, counts, errors)?
   - Are there any new import requirements since the original implementation?

**⚠️ REQUIRES INVESTIGATION** - The original function was 200+ lines with complex WBS creation logic. Need to determine which parts are still needed before implementing the fix.

#### 6. `compare_budget_snapshots` - REMOVED COLUMNS
**Previous Version** (20250310143526): Included `snapshot_1_factor` and `snapshot_2_factor` columns
**New Version** (Declarative): Factor columns removed from return type
**Status**: ⚠️ DATA LOSS - Factor comparison functionality removed

**🔧 IMPLEMENTATION STEPS:**

1. **Get the original function signature:**
   ```bash
   # View the original function with factor columns
   grep -A 20 "compare_budget_snapshots" supabase/migrations/20250310143526_add_budget_management_functions.sql
   ```

2. **Check if factor columns are used in the application:**
   ```bash
   # Search for factor usage in snapshot comparisons
   grep -r "snapshot.*factor\|factor.*snapshot" src/
   grep -r "compare_budget_snapshots" src/
   ```

3. **Restore the complete function with factor columns:**
   ```sql
   CREATE OR REPLACE FUNCTION public.compare_budget_snapshots (
       p_snapshot_id_1 uuid,
       p_snapshot_id_2 uuid
   ) RETURNS TABLE (
       wbs_library_item_id uuid,
       snapshot_1_quantity numeric,
       snapshot_1_cost numeric,
       snapshot_1_factor numeric,  -- RESTORE THIS
       snapshot_2_quantity numeric,
       snapshot_2_cost numeric,
       snapshot_2_factor numeric,  -- RESTORE THIS
       quantity_diff numeric,
       cost_diff numeric,
       factor_diff numeric,       -- ADD THIS
       percent_change numeric
   ) LANGUAGE plpgsql SECURITY DEFINER
   SET search_path TO '' AS $function$
   BEGIN
       RETURN QUERY
       SELECT
           COALESCE(s1.wbs_library_item_id, s2.wbs_library_item_id) AS wbs_library_item_id,
           s1.quantity AS snapshot_1_quantity,
           s1.unit_rate AS snapshot_1_cost,
           s1.factor AS snapshot_1_factor,
           s2.quantity AS snapshot_2_quantity,
           s2.unit_rate AS snapshot_2_cost,
           s2.factor AS snapshot_2_factor,
           COALESCE(s2.quantity, 0) - COALESCE(s1.quantity, 0) AS quantity_diff,
           COALESCE(s2.unit_rate, 0) - COALESCE(s1.unit_rate, 0) AS cost_diff,
           COALESCE(s2.factor, 0) - COALESCE(s1.factor, 0) AS factor_diff,
           CASE
               WHEN COALESCE(s1.unit_rate, 0) = 0 THEN NULL
               ELSE ((COALESCE(s2.unit_rate, 0) - COALESCE(s1.unit_rate, 0)) / s1.unit_rate) * 100
           END AS percent_change
       FROM public.budget_snapshot_line_item s1
       FULL OUTER JOIN public.budget_snapshot_line_item s2
           ON s1.wbs_library_item_id = s2.wbs_library_item_id
       WHERE s1.budget_snapshot_id = p_snapshot_id_1
       AND s2.budget_snapshot_id = p_snapshot_id_2;
   END;
   $function$;
   ```

4. **Update application code if needed:**
   - Add handling for the restored factor columns in UI components
   - Update TypeScript interfaces to include factor fields

## Functions with Parameter Changes

#### 7. `accept_invite` - PARAMETER TYPE CHANGE
**Previous**: `token_param char(64)`
**New**: `token_param character`
**Status**: ⚠️ POTENTIAL ISSUE - Character length constraint removed

**🔧 IMPLEMENTATION STEPS:**

1. **Check current invite token usage:**
   ```bash
   # Find how invite tokens are generated and used
   grep -r "invite.*token\|token.*invite" src/
   grep -r "char(64)\|character(64)" supabase/
   ```

2. **Verify token length requirements:**
   ```bash
   # Check invite table definition
   grep -A 10 -B 5 "token" supabase/migrations/*invite*.sql
   ```

3. **Fix the parameter type:**
   ```sql
   CREATE OR REPLACE FUNCTION public.accept_invite (
       token_param character(64)  -- RESTORE LENGTH CONSTRAINT
   ) RETURNS json LANGUAGE plpgsql SECURITY DEFINER
   SET search_path TO '' AS $function$
   -- ... rest of function unchanged
   ```

4. **Verify the fix:**
   - Ensure invite tokens are exactly 64 characters
   - Test that function rejects invalid token lengths

#### 8. `get_clients_with_permissions` - RETURN COLUMNS REDUCED
**Previous**: 16 columns including detailed metadata
**New**: 4 columns (client_id, name, description, user_role)
**Status**: ⚠️ DATA LOSS - Lost important metadata columns

**📋 INVESTIGATION STEPS:**

1. **Get the original function signature:**
   ```bash
   # View the complete original function
   cat supabase/migrations/20250510000000_create_get_clients_with_permissions.sql
   ```

2. **Find UI components that use this function:**
   ```bash
   # Search for usage in client listing pages
   grep -r "get_clients_with_permissions" src/
   find src/ -name "*client*" -type f | xargs grep -l "client_id\|name\|description"
   ```

3. **Check which columns are actually displayed:**
   ```bash
   # Look for client metadata usage in components
   grep -r "logo_url\|client_url\|internal_url\|project_count\|created_at" src/
   ```

4. **Determine which columns to restore:**
   - **Essential**: `client_id`, `name`, `description`, `user_role` (already present)
   - **Likely needed**: `logo_url`, `project_count`, `is_client_admin`, `is_org_admin`
   - **Possibly needed**: `client_url`, `internal_url`, `created_at`, `updated_at`

5. **Questions for stakeholders:**
   - Which client metadata is displayed in the client listing UI?
   - Are client URLs and internal URLs still used?
   - Is project count displayed in client cards/lists?
   - Are admin status indicators shown in the UI?

**⚠️ REQUIRES UI INVESTIGATION** - Need to check which columns are actually used in client listing components before restoring the full function.

## Functions Correctly Preserved

### ✅ Core Access Control Functions (All Correct)
- `has_entity_access` - ✅ Preserved correctly
- `has_entity_role` - ✅ Preserved correctly  
- `current_user_has_entity_access` - ✅ Preserved correctly
- `current_user_has_entity_role` - ✅ Preserved correctly
- `get_effective_role` - ✅ Preserved correctly
- `get_entity_ancestors` - ✅ Preserved correctly

### ✅ Audit Functions (All Correct)
- `audit_budget_line_item_changes` - ✅ Preserved correctly
- `audit_wbs_library_item_changes` - ✅ Preserved correctly
- All other audit functions - ✅ Preserved correctly

### ✅ Utility Functions (All Correct)
- `handle_new_user` - ✅ Preserved correctly
- `update_updated_at_column` - ✅ Preserved correctly
- `add_creator_as_admin` - ✅ Preserved correctly

## New Functions (Need Verification)

#### 9. `is_stage_ready_for_completion` - NEW
**Status**: ✅ NEW FUNCTION - Appears to be correctly implemented

#### 10. `log_initial_checklist_item_status` - NEW  
**Status**: ✅ NEW FUNCTION - Appears to be correctly implemented

#### 11. `set_gateway_checklist_item_latest` - NEW
**Status**: ✅ NEW FUNCTION - Appears to be correctly implemented

## Recommendations

### Immediate Actions Required:

1. **✅ READY TO IMPLEMENT: Fix `create_organization` return type** - Complete implementation provided above
2. **✅ READY TO IMPLEMENT: Fix `complete_project_stage` return type** - Complete implementation provided above
3. **📋 NEEDS INVESTIGATION: Restore `import_budget_data` full functionality** - Investigation steps provided above
4. **✅ READY TO IMPLEMENT: Restore `compare_budget_snapshots` factor columns** - Complete implementation provided above
5. **✅ READY TO IMPLEMENT: Fix `accept_invite` parameter type** - Simple fix provided above
6. **📋 NEEDS INVESTIGATION: Restore `get_clients_with_permissions` columns** - Investigation steps provided above

### Testing Required:
- Test all functions with changed signatures
- Verify application code compatibility
- Test RLS policies that depend on these functions
- Verify audit logging still works correctly

### Low Priority:
- Review new functions for completeness
- Verify all SECURITY DEFINER settings are appropriate
- Check search_path settings consistency

## Detailed Function-by-Function Analysis

### Access Control Functions (Core RLS System)

#### `can_access_client` ✅ PRESERVED
- **Status**: Correctly preserved
- **Security**: SECURITY DEFINER maintained
- **Logic**: Unchanged - delegates to `current_user_has_entity_access`

#### `can_access_project` ✅ PRESERVED
- **Status**: Correctly preserved
- **Security**: SECURITY DEFINER maintained
- **Logic**: Unchanged - delegates to `current_user_has_entity_access`

#### `can_modify_client` ✅ PRESERVED
- **Status**: Correctly preserved
- **Security**: SECURITY DEFINER maintained
- **Logic**: Unchanged - delegates to `current_user_has_entity_role`

#### `can_modify_client_wbs` ✅ PRESERVED
- **Status**: Correctly preserved
- **Security**: SECURITY DEFINER maintained
- **Logic**: Unchanged - requires admin role

#### `can_modify_project` ✅ PRESERVED
- **Status**: Correctly preserved
- **Security**: SECURITY DEFINER maintained
- **Logic**: Unchanged - delegates to `current_user_has_entity_role`

#### `is_client_admin` ⚠️ LOGIC SIMPLIFIED
**Previous Version**: Complex logic checking direct admin role AND organization admin fallback
**New Version**: Simplified to only check direct client admin role OR organization admin
**Status**: ⚠️ POTENTIAL ISSUE - May have lost some edge case handling

**📋 INVESTIGATION STEPS:**

1. **Compare the implementations:**
   ```bash
   # View original implementation
   grep -A 20 "is_client_admin" supabase/migrations/20250309203000_create_membership_system.sql

   # View current implementation
   grep -A 20 "is_client_admin" supabase/migrations/20250708095026_declarative_schemas.sql
   ```

2. **Check for usage in RLS policies:**
   ```bash
   # Find where is_client_admin is used in security policies
   grep -r "is_client_admin" supabase/migrations/
   grep -r "is_client_admin" src/
   ```

3. **Test the logic difference:**
   - **Original**: May have had more complex hierarchy checking
   - **Current**: Simple direct admin OR organization admin check
   - **Question**: Are there edge cases where the original logic was needed?

4. **Verify current implementation is sufficient:**
   ```sql
   -- Current implementation should be:
   CREATE OR REPLACE FUNCTION public.is_client_admin (client_id_param uuid)
   RETURNS boolean LANGUAGE plpgsql SECURITY DEFINER
   SET search_path TO '' AS $function$
   DECLARE
       org_id_var uuid;
   BEGIN
       -- Check if user has direct admin role on the client
       IF public.current_user_has_entity_role('client', client_id_param, 'admin') THEN
           RETURN TRUE;
       END IF;

       -- Check if user has admin role on the organization
       SELECT org_id INTO org_id_var
       FROM public.client
       WHERE client_id = client_id_param;

       RETURN public.current_user_has_entity_role('organization', org_id_var, 'admin');
   END;
   $function$;
   ```

**✅ LIKELY CORRECT** - The simplified logic appears to cover the main use cases. Only investigate further if RLS policies are failing.

#### `is_project_owner` ⚠️ LOGIC SIMPLIFIED
**Previous Version**: Complex hierarchy checking with multiple fallbacks
**New Version**: Simplified logic but appears functionally equivalent
**Status**: ✅ ACCEPTABLE - Logic simplified but functionality preserved

#### `is_org_admin_for_project` ✅ PRESERVED
- **Status**: Correctly preserved
- **Security**: SECURITY DEFINER maintained
- **Logic**: Unchanged

### Membership and Invite Functions

#### `check_membership_redundancy` ✅ PRESERVED
- **Status**: Correctly preserved
- **Security**: SECURITY DEFINER maintained
- **Logic**: Complex redundancy checking logic preserved

#### `apply_pending_invites` ✅ PRESERVED
- **Status**: Correctly preserved
- **Security**: SECURITY DEFINER maintained
- **Logic**: Trigger function for auto-applying invites preserved

### Budget Management Functions

#### `create_budget_snapshot` ✅ PRESERVED
- **Status**: Correctly preserved
- **Security**: SECURITY DEFINER maintained
- **Logic**: Complex snapshot creation logic preserved

#### `revert_to_budget_snapshot` ✅ PRESERVED
- **Status**: Correctly preserved
- **Security**: SECURITY DEFINER maintained
- **Logic**: Snapshot reversion logic preserved

#### `upsert_budget_line_item` ✅ PRESERVED
- **Status**: Correctly preserved
- **Security**: SECURITY DEFINER maintained
- **Logic**: Complex upsert logic with cost calculations preserved

### Member Access Functions

#### `get_client_members` ⚠️ RETURN TYPE SIMPLIFIED
**Previous Version**: Returned 9 columns including avatar_url, timestamps, membership_id, access_via
**New Version**: Returns only 4 columns (user_id, email, full_name, role)
**Status**: ⚠️ DATA LOSS - Lost important metadata for UI display

**📋 INVESTIGATION STEPS:**

1. **Get the original function signature:**
   ```bash
   # View the complete original function
   cat supabase/migrations/20250506075546_create_client_members_rpc.sql
   ```

2. **Find member listing UI components:**
   ```bash
   # Search for member display components
   grep -r "get_client_members" src/
   find src/ -name "*member*" -type f | xargs grep -l "avatar\|membership_id\|access_via"
   ```

3. **Check if metadata columns are used:**
   ```bash
   # Look for avatar, timestamps, and access info usage
   grep -r "avatar_url\|created_at\|updated_at\|access_via\|membership_id" src/
   ```

4. **Determine restoration approach:**
   - **Option A**: Restore full function with all 9 columns
   - **Option B**: Create a simplified version with only needed columns
   - **Option C**: Keep current version if metadata isn't used

5. **Questions for stakeholders:**
   - Are user avatars displayed in member lists?
   - Is "access via" information (direct vs inherited) shown to users?
   - Are member join dates displayed?
   - Is membership management (edit/delete) functionality needed?

**⚠️ REQUIRES UI INVESTIGATION** - Check member listing components to determine which columns are actually needed.

#### `profiles_with_client_access` ✅ PRESERVED
- **Status**: Correctly preserved
- **Security**: SECURITY DEFINER maintained
- **Return Type**: Simplified to 3 columns (was 10) but this appears intentional

#### `profiles_with_project_access` ✅ PRESERVED
- **Status**: Correctly preserved
- **Security**: SECURITY DEFINER maintained
- **Return Type**: Simplified to 3 columns (was 9) but this appears intentional

## Summary of Critical Issues

### Must Fix Immediately:
1. **`create_organization`** - Breaking change in return type (json → uuid)
2. **`complete_project_stage`** - Breaking change in return type (uuid → boolean)
3. **`import_budget_data`** - Lost complex WBS creation and error handling logic
4. **`compare_budget_snapshots`** - Lost factor columns in return type

### Should Review:
5. **✅ READY TO IMPLEMENT: `accept_invite`** - Simple parameter fix provided above
6. **📋 NEEDS INVESTIGATION: `get_clients_with_permissions`** - Investigation steps provided above
7. **📋 NEEDS INVESTIGATION: `get_client_members`** - Investigation steps provided above
8. **📋 NEEDS VERIFICATION: `is_client_admin`** - Verification steps provided above

### Verification Needed:
- Test all changed functions with existing application code
- Verify RLS policies still work correctly
- Check that UI components have all needed data
- Ensure audit logging captures all necessary information

The declarative schema generation has preserved the core security and access control functions correctly, but has introduced several breaking changes in return types and simplified some complex business logic that may need to be restored.

## Quick Implementation Guide

### Ready to Implement Immediately (Copy-Paste Solutions):

1. **`create_organization`** - Lines 103-132 above
2. **`complete_project_stage`** - Lines 173-213 above
3. **`compare_budget_snapshots`** - Lines 286-329 above
4. **`accept_invite`** - Lines 357-360 above

### Requires Investigation First:

1. **`import_budget_data`** - Follow investigation steps in lines 227-260
2. **`get_clients_with_permissions`** - Follow investigation steps in lines 384-403
3. **`get_client_members`** - Follow investigation steps in lines 555-570
4. **`is_client_admin`** - Follow verification steps in lines 502-539

### Implementation Order Recommendation:

1. **Start with the ready-to-implement fixes** (breaking changes first)
2. **Run the investigation steps** for the complex functions
3. **Test each fix** before moving to the next
4. **Update application code** as needed for any interface changes
