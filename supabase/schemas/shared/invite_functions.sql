-- Invite Management Functions
-- This file contains functions for invite operations
CREATE OR REPLACE FUNCTION "public"."accept_invite" ("token_param" char(64)) RETURNS "json" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE 
	v_invite public.invite %rowtype;
	v_user_id uuid;
	v_entity_type public.entity_type;
	v_entity_id uuid;
	v_role public.membership_role;
	v_resource_type text;
BEGIN 
	-- Get the current user ID
	v_user_id := auth.uid();
	IF v_user_id IS NULL THEN 
		RAISE EXCEPTION 'Not authenticated';
	END IF;
	
	-- Get the invite
	SELECT * INTO v_invite
	FROM public.invite
	WHERE token_hash = token_param
	AND status = 'pending'
	AND expires_at > now();
	
	IF NOT FOUND THEN 
		RAISE EXCEPTION 'Invalid or expired invite token';
	END IF;
	
	-- Store resource_type as text to avoid casting issues
	v_resource_type := v_invite.resource_type::text;
	
	IF v_resource_type = 'organization' THEN
		v_entity_type := 'organization'::public.entity_type;
		v_entity_id := v_invite.resource_id;
		IF v_invite.role = 'member' THEN
			v_role := 'viewer'::public.membership_role;
		ELSE
			v_role := v_invite.role::public.membership_role;
		END IF;
	ELSIF v_resource_type = 'client' THEN
		v_entity_type := 'client'::public.entity_type;
		v_entity_id := v_invite.resource_id;
		v_role := v_invite.role::public.membership_role;
	ELSIF v_resource_type = 'project' THEN
		v_entity_type := 'project'::public.entity_type;
		v_entity_id := v_invite.resource_id;
		v_role := v_invite.role::public.membership_role;
	ELSE
		RAISE EXCEPTION 'Invalid resource type';
	END IF;
	
	-- Add membership
	INSERT INTO public.membership(user_id, role, entity_type, entity_id)
	VALUES (v_user_id, v_role, v_entity_type, v_entity_id)
	ON CONFLICT (entity_type, entity_id, user_id) DO NOTHING;
	
	-- Update invite status
	UPDATE public.invite
	SET status = 'accepted'::public.invite_status,
		updated_at = now(),
		updated_by = v_user_id
	WHERE invite_id = v_invite.invite_id;
	
	RETURN json_build_object(
		'success', true,
		'message', 'Invite accepted successfully',
		'resource_type', v_invite.resource_type,
		'resource_id', v_invite.resource_id
	);
	
EXCEPTION
	WHEN OTHERS THEN 
		RETURN json_build_object('success', false, 'message', SQLERRM);
END;
$$;

ALTER FUNCTION "public"."accept_invite" ("token_param" character) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."accept_invite" ("token_param" character) IS 'Accepts an invite using a token and adds the user to the appropriate entity';
